#!/usr/bin/env python3
"""
Surge Tools MCP Server

This MCP server provides tools for managing Surge proxy configurations,
including rule merging, module merging, and configuration management.
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
)

from rule_merger import RuleMerger
from module_merger import ModuleMerger

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SurgeToolsMCPServer:
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self.rule_merger = None
        self.module_merger = None
        self._initialize_mergers()
    
    def _initialize_mergers(self):
        """Initialize the rule and module mergers"""
        try:
            if os.path.exists(self.config_path):
                self.rule_merger = RuleMerger(self.config_path)
                self.module_merger = ModuleMerger(self.config_path)
                logger.info(f"Initialized mergers with config: {self.config_path}")
            else:
                logger.warning(f"Config file not found: {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to initialize mergers: {e}")

    async def merge_surge_rules(self, rule_set_name: str) -> Dict[str, Any]:
        """Merge Surge rules for a specific rule set"""
        if not self.rule_merger:
            raise ValueError("Rule merger not initialized. Check config file.")
        
        try:
            merged_data = self.rule_merger.merge_rules(rule_set_name)
            if not merged_data:
                return {"error": f"Failed to merge rules for {rule_set_name}"}
            
            # Calculate statistics
            rules = merged_data['rules']
            stats = {}
            total = 0
            for rule_type, rule_list in rules.items():
                count = len(rule_list)
                stats[rule_type] = count
                total += count
            stats['TOTAL'] = total
            
            return {
                "success": True,
                "rule_set_name": rule_set_name,
                "metadata": merged_data['metadata'],
                "statistics": stats,
                "rules": merged_data['rules']
            }
        except Exception as e:
            logger.error(f"Error merging rules for {rule_set_name}: {e}")
            return {"error": str(e)}

    async def merge_surge_modules(self, module_name: str) -> Dict[str, Any]:
        """Merge Surge modules for a specific module"""
        if not self.module_merger:
            raise ValueError("Module merger not initialized. Check config file.")
        
        try:
            merged_data = self.module_merger.merge_module(module_name)
            if not merged_data:
                return {"error": f"Failed to merge module {module_name}"}
            
            return {
                "success": True,
                "module_name": module_name,
                "metadata": merged_data['metadata'],
                "content": self.module_merger.format_module_content(merged_data)
            }
        except Exception as e:
            logger.error(f"Error merging module {module_name}: {e}")
            return {"error": str(e)}

    async def list_available_rulesets(self) -> Dict[str, Any]:
        """List all available rule sets from configuration"""
        if not self.rule_merger:
            return {"error": "Rule merger not initialized"}
        
        try:
            rulesets = {}
            for name, config in self.rule_merger.config['rules']['sources'].items():
                rulesets[name] = {
                    "name": config.get('name', name),
                    "description": config.get('description', ''),
                    "author": config.get('author', ''),
                    "urls_count": len(config.get('urls', []))
                }
            
            return {
                "success": True,
                "rulesets": rulesets,
                "total_count": len(rulesets)
            }
        except Exception as e:
            return {"error": str(e)}

    async def list_available_modules(self) -> Dict[str, Any]:
        """List all available modules from configuration"""
        if not self.module_merger:
            return {"error": "Module merger not initialized"}
        
        try:
            modules = {}
            if 'modules' in self.module_merger.config:
                for name, config in self.module_merger.config['modules']['sources'].items():
                    modules[name] = {
                        "name": config.get('name', name),
                        "description": config.get('desc', ''),
                        "author": config.get('author', ''),
                        "urls_count": len(config.get('urls', []))
                    }
            
            return {
                "success": True,
                "modules": modules,
                "total_count": len(modules)
            }
        except Exception as e:
            return {"error": str(e)}

    async def get_rule_stats(self, rule_set_name: str) -> Dict[str, Any]:
        """Get statistics for a specific rule set without full merge"""
        try:
            merged_data = await self.merge_surge_rules(rule_set_name)
            if "error" in merged_data:
                return merged_data
            
            return {
                "success": True,
                "rule_set_name": rule_set_name,
                "statistics": merged_data["statistics"],
                "metadata": merged_data["metadata"]
            }
        except Exception as e:
            return {"error": str(e)}

# Create the MCP server
server = Server("surge-tools")
surge_tools = SurgeToolsMCPServer()

@server.list_tools()
async def handle_list_tools() -> ListToolsResult:
    """List available tools"""
    return ListToolsResult(
        tools=[
            Tool(
                name="merge_surge_rules",
                description="Merge Surge rules from multiple sources for a specific rule set",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "rule_set_name": {
                            "type": "string",
                            "description": "Name of the rule set to merge (e.g., 'advertising', 'apple', 'telegram')"
                        }
                    },
                    "required": ["rule_set_name"]
                }
            ),
            Tool(
                name="merge_surge_modules",
                description="Merge Surge modules from multiple sources for a specific module",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "module_name": {
                            "type": "string",
                            "description": "Name of the module to merge (e.g., 'black-http-dns', 'generalSettings')"
                        }
                    },
                    "required": ["module_name"]
                }
            ),
            Tool(
                name="list_available_rulesets",
                description="List all available rule sets from configuration",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            Tool(
                name="list_available_modules",
                description="List all available modules from configuration",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            Tool(
                name="get_rule_stats",
                description="Get statistics for a specific rule set",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "rule_set_name": {
                            "type": "string",
                            "description": "Name of the rule set to get stats for"
                        }
                    },
                    "required": ["rule_set_name"]
                }
            )
        ]
    )

@server.call_tool()
async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
    """Handle tool calls"""
    try:
        if request.name == "merge_surge_rules":
            rule_set_name = request.arguments.get("rule_set_name")
            if not rule_set_name:
                raise ValueError("rule_set_name is required")
            
            result = await surge_tools.merge_surge_rules(rule_set_name)
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2))])
        
        elif request.name == "merge_surge_modules":
            module_name = request.arguments.get("module_name")
            if not module_name:
                raise ValueError("module_name is required")
            
            result = await surge_tools.merge_surge_modules(module_name)
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2))])
        
        elif request.name == "list_available_rulesets":
            result = await surge_tools.list_available_rulesets()
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2))])
        
        elif request.name == "list_available_modules":
            result = await surge_tools.list_available_modules()
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2))])
        
        elif request.name == "get_rule_stats":
            rule_set_name = request.arguments.get("rule_set_name")
            if not rule_set_name:
                raise ValueError("rule_set_name is required")
            
            result = await surge_tools.get_rule_stats(rule_set_name)
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2))])
        
        else:
            raise ValueError(f"Unknown tool: {request.name}")
    
    except Exception as e:
        logger.error(f"Error handling tool call {request.name}: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps({"error": str(e)}, indent=2))],
            isError=True
        )

async def main():
    """Main entry point"""
    # Change to the project directory
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(project_dir)
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="surge-tools",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
