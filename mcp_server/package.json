{"name": "surge-tools-mcp-server", "version": "1.0.0", "description": "MCP Server for Surge Tools - Network proxy configuration management", "main": "surge_tools_mcp_server.py", "scripts": {"start": "python surge_tools_mcp_server.py", "install-deps": "pip install -r requirements.txt"}, "keywords": ["mcp", "surge", "proxy", "network", "configuration", "rules", "modules"], "author": "gys619", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/gys619/surge-tools"}, "mcp": {"server": {"command": "python", "args": ["surge_tools_mcp_server.py"], "env": {}}}}