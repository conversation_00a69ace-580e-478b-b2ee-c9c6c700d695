rules:
  output_dir: "./rules"    # 规则文件输出目录
  sources:
    advertising:           # 广告规则集配置
      name: "Advertising"  # 规则集名称
      author: "gys619"     # 作者
      repo: "https://github.com/gys619/surge-tools"  # 仓库地址
      description: "分流规则中含有URL-REGEX类型，建议搭配MITM使用"  # 规则集描述
      urls:  # 需要合并的规则源
        # - "https://raw.githubusercontent.com/Centralmatrix3/Matrix-io/master/Surge/Ruleset/AdBlock.list"
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Advertising/Advertising_All_No_Resolve.list"
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Surge/Privacy/Privacy_All_No_Resolve.list"
        - "https://github.com/blackmatrix7/ios_rule_script/raw/refs/heads/master/rule/Surge/Hijacking/Hijacking_Resolve.list"
        - "https://github.com/thNylHx/Tools/raw/main/Ruleset/Surge/Block/Ads_ml.list"
      exclude_rules:  # 需要排除的单条规则
        - ""
      exclude_rule_sets:  # 需要排除的规则集
        - ""
      rule_preference:  # 规则优先级顺序
        - "DOMAIN"
        - "DOMAIN-SUFFIX"
        - "DOMAIN-KEYWORD"

    netflix:              # Netflix 规则集配置
      name: "Netflix"     # 规则集名称
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "Netflix 分流规则"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Netflix/Netflix.list"
      exclude_rules: []   # 空数组表示没有需要排除的规则
      rule_preference:
        - "DOMAIN"
        - "DOMAIN-SUFFIX"
        - "IP-CIDR"

    youtube:              # YouTube 规则集配置
      name: "YouTube"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "YouTube 分流规则"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/YouTube/YouTube.list"
        - "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/YouTube.list"
      exclude_rules: []
      rule_preference:
        - "DOMAIN-SUFFIX"
        - "DOMAIN"
        - "IP-CIDR"

    telegram:             # Telegram 规则集配置
      name: "Telegram"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "Telegram 分流规则"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Telegram/Telegram.list"
        # - "https://raw.githubusercontent.com/DivineEngine/Profiles/master/Surge/Ruleset/Extra/Telegram/Telegram.list"
      exclude_rules: []
      rule_preference:
        - "DOMAIN-SUFFIX"
        - "DOMAIN-KEYWORD"
        - "IP-CIDR"
        - "IP-CIDR6"

    apple:                # Apple 服务规则集配置
      name: "Apple"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "Apple 分流规则，包含 App Store、Apple Music 等服务"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Apple/Apple.list"
        # - "https://raw.githubusercontent.com/DivineEngine/Profiles/master/Surge/Ruleset/Extra/Apple/Apple.list"
      exclude_rules: []
      rule_preference:
        - "DOMAIN"
        - "DOMAIN-SUFFIX"
        - "DOMAIN-KEYWORD"
        - "IP-CIDR"
    GlobalMedia:
      name: "GlobalMedia"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "GlobalMedia分流规则(youtube,youtube music)"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/YouTube/YouTube.list"
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/YouTubeMusic/YouTubeMusic.list"

      exclude_rules: []
      rule_preference:
        - "DOMAIN"
        - "DOMAIN-SUFFIX"
        - "DOMAIN-KEYWORD"
        - "IP-CIDR"
    ChinaMedia:
      name: "ChinaMedia"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "ChinaMedia分流规则(bilibili,微博,小红书,快手,抖音,TikTok,Kuaishou,Douyin,Xiaohongshu,Weibo,Kuaishou,Douyin,Xiaohongshu)"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/BiliBili/BiliBili.list"
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/NetEaseMusic/NetEaseMusic.list"
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/TencentVideo/TencentVideo.list"
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/DouYin/DouYin.list"
    OpenAI:
      name: "AI"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      description: "AI分流规则"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/OpenAI/OpenAI.list"
        - "https://raw.githubusercontent.com/gys619/surge-tools/refs/heads/main/rules/AI.list"
        



modules:
  output_dir: "./modules"  # 模块文件输出目录
  sources:
    black-http-dns:      # MITM 模块配置
      name: "拦截httpdns"  # 模块名称
      desc: "分流规则中含有URL-REGEX类型的MITM模块"  # 模块描述
      author: "gys619"      # 作者
      repo: "https://github.com/gys619/surge-tools"  # 仓库地址
      urls:  # 模块源
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rewrite/Surge/BlockHTTPDNS/BlockHTTPDNS.sgmodule"
        - "https://raw.githubusercontent.com/gys619/surge-tools/main/modules/Block_HTTPDNS.sgmodule"
      exclude_rules:  # 排除特定的 MITM 主机名或规则
        - "*.example.com"
        - "api.example.com"
      exclude_module_sets:  # 排除其他模块文件中的规则
        - "https://example.com/other-mitm-rules.sgmodule"
      section_preference:  # 模块段落优先级
        - "MITM"         # MITM 配置段
        - "URL-REGEX"    # URL 正则表达式段
        - "General"      # 通用配置段
        - "Script"       # 脚本段

    generalSettings:              # 脚本模块配置
      name: "GeneralSettings"
      desc: "通用设置模块"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      urls:
        - "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rewrite/Surge/General/General.sgmodule"
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Official/%F0%9F%8D%9F%20%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97.official.sgmodule"
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Official/%E6%8B%A6%E6%88%AAHTTPDNS.official.sgmodule"
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Official/%F0%9F%8D%9F%20DNS%20%E5%88%86%E6%B5%81.official.sgmodule"
      exclude_rules:     # 排除特定的脚本
        - "ExampleScript.js"
        - "UnwantedScript.js"
      exclude_module_sets:
        - ""
      section_preference:
        - "Script"
        - "MITM"
        - "General"
    AppRemoveAds:
      name: "AppRemoveAds"
      desc: "app去广告模块(bilibili,微博,菜鸟,12306,淘宝,阿里云盘,拼多多,小红书,高德地图,滴滴出行,彩云天气,闲鱼,keep,微信,爱优腾芒,汽水音乐,微博,快手,qq音乐)"
      author: "gys619"
      repo: "https://github.com/gys619/surge-tools"
      urls:
        #哔哩哔哩
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 菜鸟
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E8%8F%9C%E9%B8%9F%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 12306
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/12306%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 淘宝
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E6%B7%98%E5%AE%9D%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 阿里云盘
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E9%98%BF%E9%87%8C%E4%BA%91%E7%9B%98%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 拼多多
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E6%8B%BC%E5%A4%9A%E5%A4%9A%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 小红书
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%B0%8F%E7%BA%A2%E4%B9%A6%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 高德地图
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E9%AB%98%E5%BE%B7%E5%9C%B0%E5%9B%BE%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 滴滴出行
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E6%BB%B4%E6%BB%B4%E5%87%BA%E8%A1%8C%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 彩云天气
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%BD%A9%E4%BA%91%E5%A4%A9%E6%B0%94%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 闲鱼
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E9%97%B2%E9%B1%BC%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # keep
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/Keep%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 微信公众号去广告
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 微信小程序去广告
        # - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%BE%AE%E4%BF%A1%E5%B0%8F%E7%A8%8B%E5%BA%8F%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 爱优腾芒
        - "https://raw.githubusercontent.com/thNylHx/Tools/refs/heads/main/Surge/Module/Video.sgmodule"
        # 京东
        # - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E4%BA%AC%E4%B8%9C%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 汽水音乐
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E6%B1%BD%E6%B0%B4%E9%9F%B3%E4%B9%90%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 微博
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%BE%AE%E5%8D%9A%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # 快手
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/%E5%BF%AB%E6%89%8B%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        # qq音乐
        - "https://raw.githubusercontent.com/QingRex/LoonKissSurge/refs/heads/main/Surge/Beta/QQ%E9%9F%B3%E4%B9%90%E5%8E%BB%E5%B9%BF%E5%91%8A.beta.sgmodule"
        
      exclude_rules:    
        - "example.com"    # 如果不需要排除规则，可以删除此项
      exclude_sections:         # 如果不需要排除段落，可以删除此项
        "模块名称":
          - "line:16-18"
          - "段落名称"
      exclude_module_sets:      # 如果不需要排除模块，可以删除此项
        - "https://example.com/module1.sgmodule"
      section_preference:       # 如果不需要指定顺序，可以删除此项
        - "General"
        - "Host"
        - "MITM"


