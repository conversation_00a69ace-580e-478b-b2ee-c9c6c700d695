{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "mcp-server-time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone=Asia/Shanghai"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "D:/workspace/Aother/mcp-shrimp-task-manager/data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSA855xpkmFI9AsvFqac4EsH0Y5ZNRb"}, "description": "Brave search server for web search capabilities"}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "Fetch server for retrieving web content and converting to markdown"}}}