# 深巷有喵的「Clash_Max」配置
# 日期：2024-10-04
# 版本：2.1.6
# 配置作者：https://github.com/Rabbit-Spec
# 适用版本：所有使用 Mihomo 内核的程序

port: 7890
socks-port: 7891
mixed-port: 7892
redir-port: 7893
tproxy-port: 7894

unified-delay: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 24
tcp-concurrent: true
find-process-mode: strict
global-client-fingerprint: chrome

allow-lan: true
mode: rule
log-level: info
ipv6: true
udp: true

external-controller: 0.0.0.0:9090
# external-ui: ui
# external-ui-url: 'https://github.com/MetaCubeX/metacubexd/archive/refs/heads/gh-pages.zip'

geox-url:
  # geoip: 'https://raw.githubusercontent.com/Loyalsoldier/v2ray-rules-dat/release/geoip.dat'
  # geosite: 'https://raw.githubusercontent.com/Loyalsoldier/v2ray-rules-dat/release/geosite.dat'
  mmdb: 'https://gitlab.com/Masaiki/GeoIP2-CN/-/raw/release/Country.mmdb'
  asn: 'https://gitlab.com/Loon0x00/loon_data/-/raw/main/geo/GeoLite2-ASN.mmdb'

profile:
  store-selected: true
  store-fake-ip: true

sniffer:
  enable: true
  force-dns-mapping: true
  parse-pure-ip: true
  override-destination: true
  sniff:
    HTTP:
      ports: [80, 8080-8880]
      override-destination: true
    TLS:
      ports: [443, 8443]
    QUIC:
      ports: [443, 8443]
  force-domain:
    - +.v2ex.com

  skip-domain:
    - Mijia Cloud

tun:
  enable: true
  stack: system
  dns-hijack:
    - any:53
  auto-route: true
  auto-detect-interface: true

dns:
  enable: true
  listen: 0.0.0.0:1053
  ipv6: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter:
    - "*"
    - +.lan
  default-nameserver:
    - *********
    - ************
  nameserver:
    - https://*********/dns-query#h3=true
    - https://*********/dns-query#h3=true

# 锚点 - 节点订阅的参数 [每24小时更新一次订阅节点，每 60 秒一次健康检查]
NodeParam: &NodeParam {type: http, interval: 86400, health-check: {enable: true, url: 'http://www.google.com/generate_204', interval: 60}}

# 锚点 - 节点订阅
proxy-providers: 
  我的节点:
    url: '机场的订阅地址'
    <<: *NodeParam
    path: './proxy_provider/Providers.yaml'

# 锚点 - 节点筛选组
FilterCustom: &FilterCustom '^(?=.*((?i)Custom|Custom|(\b(Custom|Custom)\b))).*$'
FilterHK: &FilterHK '^(?=.*((?i)🇭🇰|香港|(\b(HK|Hong)\b))).*$'
FilterJP: &FilterJP '^(?=.*((?i)🇯🇵|日本|川日|东京|大阪|泉日|埼玉|(\b(JP|Japan)\b))).*$'
FilterKR: &FilterKR '^(?=.*((?i)🇰🇷|韩国|韓|首尔|(\b(KR|Korea)\b))).*$'
FilterSG: &FilterSG '^(?=.*((?i)🇸🇬|新加坡|狮|(\b(SG|Singapore)\b))).*$'
FilterUS: &FilterUS '^(?=.*((?i)🇺🇸|美国|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|(\b(US|United States)\b))).*$'
FilterUK: &FilterUK '^(?=.*((?i)🇬🇧|英国|伦敦|(\b(UK|United Kingdom)\b))).*$'
FilterFR: &FilterFR '^(?=.*((?i)🇫🇷|法国|(\b(FR|France)\b))).*$'
FilterDE: &FilterDE '^(?=.*((?i)🇩🇪|德国|(\b(DE|Germany)\b))).*$'
FilterGame: &FilterGame '^(?=.*((?i)游戏|🎮|(\b(GAME)\b)))(?!.*((?i)回国|校园)).*$'
FilterAll: &FilterAll '^(?=.*(.))(?!.*((?i)群|邀请|返利|循环|官网|客服|网站|网址|获取|订阅|流量|到期|机场|下次|版本|官址|备用|过期|已用|联系|邮箱|工单|贩卖|通知|倒卖|防止|国内|地址|频道|无法|说明|使用|提示|特别|访问|支持|教程|关注|更新|作者|加入|(\b(USE|USED|TOTAL|EXPIRE|EMAIL|Panel|Channel|Author)\b|(\d{4}-\d{2}-\d{2}|\d+G)))).*$'

# 锚点 - 故障转移参数 [每 10 秒一次惰性健康检查，时延超过 2 秒判定为失败，失败 3 次则自动触发健康检查]
FallBack: &FallBack {type: fallback, interval: 10, lazy: true, url: 'http://www.google.com/generate_204', disable-udp: false, timeout: 2000, max-failed-times: 3, hidden: true, include-all-providers: true}
# 锚点 - 规则参数 [每天更新一次订阅规则，更新规则时使用直连策略]
RuleSet: &RuleSet {type: http, behavior: classical, interval: 86400, format: yaml, proxy: DIRECT}

# 策略组
proxy-groups: 
  - {name: Proxy, type: select, proxies: [🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/pompurin404/mihomo-party/master/resources/icon.png'}
  - {name: Apple, type: select, proxies: [DIRECT, Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Apple_1.png'}
  - {name: OpenAI, type: select, include-all: true, filter: *FilterAll, icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/ChatGPT.png'}
  - {name: Telegram, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Telegram.png'}
  - {name: Netflix, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Netflix.png'}
  - {name: Disney+, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Disney.png'}
  - {name: YouTube, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/YouTube.png'}
  - {name: TikTok, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/TikTok.png'}
  - {name: Spotify, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Spotify.png'}
  - {name: Gamer, type: select, include-all: true, filter: *FilterGame, proxies: [DIRECT, Proxy], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Game.png'}
  - {name: Microsoft, type: select, proxies: [DIRECT, Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Microsoft.png'}
  - {name: GlobalMedia, type: select, proxies: [Proxy, 🇭🇰 香港节点, 🇯🇵 日本节点, 🇰🇷 韩国节点, 🇸🇬 新加坡节点, 🇺🇸 美国节点, 🇬🇧 英国节点, 🇫🇷 法国节点, 🇩🇪 德国节点], icon: 'https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/ForeignMedia.png'}
  - {name: CustomGroup, type: select,  include-all: true, filter: *FilterAll, icon: 'https://raw.githubusercontent.com/fmz200/wool_scripts/main/icons/menhera/Sticker_1212.png'}
  
  
  - {name: 🇭🇰 香港节点, <<: *FallBack, filter: *FilterHK}
  - {name: 🇯🇵 日本节点, <<: *FallBack, filter: *FilterJP}
  - {name: 🇰🇷 韩国节点, <<: *FallBack, filter: *FilterKR}
  - {name: 🇸🇬 新加坡节点, <<: *FallBack, filter: *FilterSG}
  - {name: 🇺🇸 美国节点, <<: *FallBack, filter: *FilterUS}
  - {name: 🇬🇧 英国节点, <<: *FallBack, filter: *FilterUK}
  - {name: 🇫🇷 法国节点, <<: *FallBack, filter: *FilterFR}
  - {name: 🇩🇪 德国节点, <<: *FallBack, filter: *FilterDE}
  - {name: Custom, <<: *FallBack, filter: *FilterCustom}

# 订阅规则
rule-providers:
  Custom:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/gys619/surge-tools/refs/heads/main/yaml/custom.yaml'
    path: './RuleSet/Custom.yaml'

  GlobalMedia:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/GlobalMedia/GlobalMedia_Classical.yaml'
    path: './RuleSet/GlobalMedia.yaml'

  ChinaMedia:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/ChinaMedia/ChinaMedia.yaml'
    path: './RuleSet/ChinaMedia.yaml'

  Netflix:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Netflix/Netflix_Classical_No_Resolve.yaml'
    path: './RuleSet/Netflix.yaml'

  Disney+:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Disney/Disney_No_Resolve.yaml'
    path: './RuleSet/Disney+.yaml'

  YouTube:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/YouTube/YouTube_No_Resolve.yaml'
    path: './RuleSet/YouTube.yaml'

  Apple:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Apple/Apple_Classical_No_Resolve.yaml'
    path: './RuleSet/Apple.yaml'

  Microsoft:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Microsoft/Microsoft_No_Resolve.yaml'
    path: './RuleSet/Microsoft.yaml'
    
  Nintendo:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Nintendo/Nintendo.yaml'
    path: './RuleSet/Nintendo.yaml'
    
  PlayStation:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/PlayStation/PlayStation.yaml'
    path: './RuleSet/PlayStation.yaml'
    
  Epic:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Epic/Epic.yaml'
    path: './RuleSet/Epic.yaml'
    
  Xbox:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Xbox/Xbox.yaml'
    path: './RuleSet/Xbox.yaml'

  TikTok:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/TikTok/TikTok_No_Resolve.yaml'
    path: './RuleSet/TikTok.yaml'

  Spotify:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Spotify/Spotify.yaml'
    path: './RuleSet/Spotify.yaml'

  OpenAI:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/OpenAI/OpenAI_No_Resolve.yaml'
    path: './RuleSet/OpenAI.yaml'
    
  Proxy:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Proxy/Proxy_Classical_No_Resolve.yaml'
    path: './RuleSet/Proxy.yaml'

  China:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ChinaMax/ChinaMax_Classical.yaml'
    path: './RuleSet/China.yaml'

  LAN:
    <<: *RuleSet
    url: 'https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Lan/Lan.yaml'
    path: './RuleSet/LAN.yaml'

# 分流规则指向
rules:
 - RULE-SET,Custom,CustomGroup
 - RULE-SET,Apple,Apple
 - RULE-SET,Spotify,Spotify
 - RULE-SET,TikTok,TikTok
 - RULE-SET,Netflix,Netflix
 - RULE-SET,Disney+,Disney+
 - RULE-SET,YouTube,YouTube
 - RULE-SET,ChinaMedia,DIRECT
 - RULE-SET,GlobalMedia,GlobalMedia
 - RULE-SET,Nintendo,Gamer
 - RULE-SET,PlayStation,Gamer
 - RULE-SET,Epic,Gamer
 - RULE-SET,Xbox,Gamer
 - RULE-SET,OpenAI,OpenAI
 - RULE-SET,Microsoft,Microsoft
 - RULE-SET,Proxy,Proxy
 - RULE-SET,China,DIRECT
 - RULE-SET,LAN,DIRECT
 - GEOIP,CN,DIRECT
 - MATCH,Proxy
