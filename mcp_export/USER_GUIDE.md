# Surge Tools MCP Server 用户指南

## 🎯 快速开始

### 1. 安装

```bash
# 克隆或下载 mcp_export 文件夹到本地
cd mcp_export

# 运行安装脚本
./install.sh
```

### 2. 在 Augment 中配置

将以下配置添加到 Augment 的设置中：

```json
{
  "mcpServers": {
    "surge-tools": {
      "command": "python",
      "args": ["/path/to/mcp_export/surge-tools-mcp-server.py"],
      "cwd": "/path/to/mcp_export",
      "env": {
        "PYTHONPATH": "/path/to/mcp_export"
      }
    }
  }
}
```

### 3. 在 Cursor 中配置

将以下配置添加到 Cursor 的 `settings.json` 中：

```json
{
  "mcp.servers": [
    {
      "name": "surge-tools",
      "command": "python",
      "args": ["/path/to/mcp_export/surge-tools-mcp-server.py"],
      "cwd": "/path/to/mcp_export"
    }
  ]
}
```

## 🛠 功能说明

### 可用工具

| 工具名称 | 功能 | 参数 |
|---------|------|------|
| `merge_surge_rules` | 合并规则集 | `rule_set_name`: 规则集名称 |
| `merge_surge_modules` | 合并模块 | `module_name`: 模块名称 |
| `list_available_rulesets` | 列出规则集 | 无 |
| `list_available_modules` | 列出模块 | 无 |
| `get_rule_stats` | 获取统计信息 | `rule_set_name`: 规则集名称 |

### 支持的规则集

- `advertising`: 广告拦截规则
- `netflix`: Netflix 分流规则
- `youtube`: YouTube 分流规则
- `telegram`: Telegram 分流规则
- `apple`: Apple 服务规则
- `GlobalMedia`: 全球媒体规则
- `ChinaMedia`: 中国媒体规则
- `OpenAI`: AI 服务规则

### 支持的模块

- `black-http-dns`: 拦截 HTTP DNS
- `generalSettings`: 通用设置模块
- `AppRemoveAds`: 应用去广告模块

## 💬 使用示例

### 在 Augment 中使用

直接使用自然语言与 AI 对话：

```
"帮我合并 advertising 规则集"
"列出所有可用的模块"
"获取 netflix 规则集的统计信息"
"合并 AppRemoveAds 模块并解释其功能"
"比较 GlobalMedia 和 ChinaMedia 规则集的规模"
```

### 在 Cursor 中使用

在代码编辑过程中，AI 会自动识别并建议使用 MCP 工具：

```python
# AI 会建议使用 MCP 工具
async def get_surge_rules():
    # 这里 AI 会提示使用 merge_surge_rules 工具
    pass
```

## 🔧 高级用法

### 批量处理

```python
# 获取所有规则集的统计信息
async def get_all_stats():
    # 先获取规则集列表
    rulesets = await mcp_client.call_tool("list_available_rulesets")
    
    stats = {}
    for name in rulesets['rulesets'].keys():
        stat = await mcp_client.call_tool("get_rule_stats", {
            "rule_set_name": name
        })
        stats[name] = stat
    
    return stats
```

### 配置生成

```python
# 生成完整的 Surge 配置
async def generate_surge_config(rule_sets, modules):
    config_parts = []
    
    # 合并规则集
    for rule_set in rule_sets:
        rules = await mcp_client.call_tool("merge_surge_rules", {
            "rule_set_name": rule_set
        })
        if rules.get('success'):
            config_parts.append(format_rules(rules))
    
    # 合并模块
    for module in modules:
        module_data = await mcp_client.call_tool("merge_surge_modules", {
            "module_name": module
        })
        if module_data.get('success'):
            config_parts.append(module_data['content'])
    
    return '\n\n'.join(config_parts)
```

## 🎨 AI 提示词建议

### 基础操作

- "显示所有可用的 Surge 规则集"
- "合并 [规则集名称] 规则集"
- "获取 [规则集名称] 的详细统计信息"
- "列出所有可用的 Surge 模块"
- "合并 [模块名称] 模块"

### 分析对比

- "比较 advertising 和 apple 规则集的规模差异"
- "分析 GlobalMedia 和 ChinaMedia 规则集的内容重叠"
- "统计所有规则集的规则数量分布"
- "找出规则数量最多的前5个规则集"

### 配置生成

- "生成一个包含广告拦截和媒体分流的 Surge 配置"
- "创建一个适合中国用户的 Surge 规则组合"
- "生成一个包含所有去广告模块的配置文件"
- "优化现有的 Surge 配置，添加必要的规则集"

### 自动化任务

- "创建一个脚本来定时更新所有规则集"
- "设置一个监控系统来检查规则集的变化"
- "生成一个 Surge 配置健康检查报告"
- "实现一个批量处理多个规则集的工具"

## 🔍 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查 Python 版本
   python --version
   
   # 检查依赖安装
   pip list | grep mcp
   
   # 查看详细错误
   python surge-tools-mcp-server.py
   ```

2. **工具调用失败**
   ```bash
   # 检查网络连接
   curl -I https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Advertising/Advertising_All_No_Resolve.list
   
   # 检查配置文件
   python -c "import yaml; yaml.safe_load(open('config.yaml'))"
   ```

3. **配置文件错误**
   ```bash
   # 验证 YAML 语法
   python -c "import yaml; print('配置文件格式正确' if yaml.safe_load(open('config.yaml')) else '配置文件格式错误')"
   ```

### 调试模式

启用详细日志：

```bash
export LOG_LEVEL=DEBUG
python surge-tools-mcp-server.py
```

### 测试连接

```bash
# 使用安装脚本中的测试功能
./install.sh
```

## 📚 更多资源

- [Surge 官方文档](https://manual.nssurge.com/)
- [MCP 协议文档](https://modelcontextprotocol.io/)
- [项目 GitHub 仓库](https://github.com/gys619/surge-tools)

## 🤝 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看本指南的故障排除部分
2. 在 GitHub 仓库提交 Issue
3. 参与社区讨论

---

**提示**: 这个 MCP 服务器专为 AI 辅助开发设计，充分利用 Augment 和 Cursor 的 AI 能力，让 Surge 配置管理变得更加智能和高效！
