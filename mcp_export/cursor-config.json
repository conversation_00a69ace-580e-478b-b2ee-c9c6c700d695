{"name": "Cursor MCP Configuration for Surge Tools", "description": "Cursor 中使用 Surge Tools MCP 服务器的配置示例", "version": "1.0.0", "mcp": {"servers": [{"name": "surge-tools", "description": "Surge 网络代理配置管理工具", "command": "python", "args": ["surge-tools-mcp-server.py"], "cwd": "/path/to/mcp_export", "env": {"PYTHONPATH": "/path/to/mcp_export", "CONFIG_PATH": "config.yaml", "LOG_LEVEL": "INFO"}, "timeout": 30000, "restart_on_exit": true}]}, "cursor_settings": {"mcp": {"enabled": true, "auto_start_servers": true, "show_tool_calls": true, "log_level": "info"}, "ai_features": {"enable_mcp_integration": true, "show_mcp_suggestions": true, "auto_complete_mcp_calls": true}}, "development_workflows": {"surge_config_management": {"description": "使用 MCP 工具管理 Surge 配置的开发工作流", "steps": ["1. 使用 list_available_rulesets 查看可用规则集", "2. 使用 merge_surge_rules 合并所需规则", "3. 使用 get_rule_stats 分析规则统计", "4. 使用 merge_surge_modules 合并模块配置", "5. 生成最终的 Surge 配置文件"], "code_templates": {"python": {"basic_usage": "# 基础 MCP 工具使用示例\nimport asyncio\nfrom mcp_client import MCPClient\n\nasync def main():\n    client = MCPClient('surge-tools')\n    \n    # 获取规则集列表\n    rulesets = await client.call_tool('list_available_rulesets')\n    print('可用规则集:', rulesets)\n    \n    # 合并特定规则集\n    rules = await client.call_tool('merge_surge_rules', {\n        'rule_set_name': 'advertising'\n    })\n    print('合并结果:', rules)\n\nif __name__ == '__main__':\n    asyncio.run(main())", "batch_processing": "# 批量处理规则集\nasync def process_all_rulesets():\n    client = MCPClient('surge-tools')\n    \n    # 获取所有规则集\n    rulesets_response = await client.call_tool('list_available_rulesets')\n    rulesets = rulesets_response.get('rulesets', {})\n    \n    results = {}\n    for ruleset_name in rulesets.keys():\n        try:\n            stats = await client.call_tool('get_rule_stats', {\n                'rule_set_name': ruleset_name\n            })\n            results[ruleset_name] = stats\n        except Exception as e:\n            print(f'处理 {ruleset_name} 时出错: {e}')\n    \n    return results", "config_generator": "# Surge 配置生成器\nclass SurgeConfigGenerator:\n    def __init__(self):\n        self.client = MCPClient('surge-tools')\n    \n    async def generate_config(self, rule_sets, modules):\n        config_parts = []\n        \n        # 处理规则集\n        for rule_set in rule_sets:\n            rules = await self.client.call_tool('merge_surge_rules', {\n                'rule_set_name': rule_set\n            })\n            if rules.get('success'):\n                config_parts.append(self._format_rules(rules))\n        \n        # 处理模块\n        for module in modules:\n            module_data = await self.client.call_tool('merge_surge_modules', {\n                'module_name': module\n            })\n            if module_data.get('success'):\n                config_parts.append(module_data['content'])\n        \n        return '\\n\\n'.join(config_parts)\n    \n    def _format_rules(self, rules_data):\n        # 格式化规则数据为 Surge 配置格式\n        formatted = []\n        for rule_type, rules in rules_data['rules'].items():\n            formatted.extend(rules)\n        return '\\n'.join(formatted)"}, "javascript": {"basic_usage": "// 基础 MCP 工具使用示例\nconst { MCPClient } = require('mcp-client');\n\nasync function main() {\n    const client = new MCPClient('surge-tools');\n    \n    try {\n        // 获取规则集列表\n        const rulesets = await client.callTool('list_available_rulesets');\n        console.log('可用规则集:', rulesets);\n        \n        // 合并特定规则集\n        const rules = await client.callTool('merge_surge_rules', {\n            rule_set_name: 'advertising'\n        });\n        console.log('合并结果:', rules);\n    } catch (error) {\n        console.error('错误:', error);\n    }\n}\n\nmain();", "web_interface": "// Web 界面集成示例\nclass SurgeToolsWebInterface {\n    constructor() {\n        this.client = new MCPClient('surge-tools');\n    }\n    \n    async loadRulesets() {\n        try {\n            const response = await this.client.callTool('list_available_rulesets');\n            return response.rulesets || {};\n        } catch (error) {\n            console.error('加载规则集失败:', error);\n            return {};\n        }\n    }\n    \n    async mergeRules(rulesetName) {\n        try {\n            const response = await this.client.callTool('merge_surge_rules', {\n                rule_set_name: rulesetName\n            });\n            return response;\n        } catch (error) {\n            console.error(`合并规则集 ${rulesetName} 失败:`, error);\n            throw error;\n        }\n    }\n    \n    async generateReport() {\n        const rulesets = await this.loadRulesets();\n        const report = {};\n        \n        for (const [name, info] of Object.entries(rulesets)) {\n            try {\n                const stats = await this.client.callTool('get_rule_stats', {\n                    rule_set_name: name\n                });\n                report[name] = {\n                    info,\n                    stats: stats.statistics || {}\n                };\n            } catch (error) {\n                report[name] = { info, error: error.message };\n            }\n        }\n        \n        return report;\n    }\n}"}}}}, "ai_prompts": {"code_generation": ["生成一个使用 MCP 工具的 Python 脚本来批量处理所有规则集", "创建一个 JavaScript 函数来合并指定的 Surge 模块", "写一个类来管理 Surge 配置的生成和更新", "实现一个 Web API 来提供 Surge 配置管理服务"], "debugging": ["帮我调试这个 MCP 工具调用失败的问题", "分析为什么规则合并返回了错误", "检查这个配置文件是否有语法问题", "优化这个批量处理脚本的性能"], "optimization": ["优化这个 Surge 配置生成器的代码结构", "改进错误处理和重试机制", "添加缓存来提高规则合并的效率", "实现配置文件的增量更新功能"]}, "integration_examples": {"vscode_extension": {"description": "在 VSCode 扩展中集成 MCP 工具", "files": {"extension.js": "// VSCode 扩展主文件\nconst vscode = require('vscode');\nconst { MCPClient } = require('mcp-client');\n\nfunction activate(context) {\n    const client = new MCPClient('surge-tools');\n    \n    // 注册命令\n    const disposable = vscode.commands.registerCommand('surge-tools.mergeRules', async () => {\n        const rulesetName = await vscode.window.showInputBox({\n            prompt: '请输入要合并的规则集名称'\n        });\n        \n        if (rulesetName) {\n            try {\n                const result = await client.callTool('merge_surge_rules', {\n                    rule_set_name: rulesetName\n                });\n                \n                // 在新文档中显示结果\n                const doc = await vscode.workspace.openTextDocument({\n                    content: JSON.stringify(result, null, 2),\n                    language: 'json'\n                });\n                await vscode.window.showTextDocument(doc);\n            } catch (error) {\n                vscode.window.showErrorMessage(`合并失败: ${error.message}`);\n            }\n        }\n    });\n    \n    context.subscriptions.push(disposable);\n}\n\nmodule.exports = { activate };"}}, "cli_tool": {"description": "命令行工具集成示例", "files": {"surge_cli.py": "#!/usr/bin/env python3\n# Surge Tools CLI\nimport argparse\nimport asyncio\nimport json\nfrom mcp_client import MCPClient\n\nasync def main():\n    parser = argparse.ArgumentParser(description='Surge Tools CLI')\n    parser.add_argument('command', choices=['list-rules', 'list-modules', 'merge-rules', 'merge-modules', 'stats'])\n    parser.add_argument('--name', help='规则集或模块名称')\n    parser.add_argument('--output', help='输出文件路径')\n    \n    args = parser.parse_args()\n    client = MCPClient('surge-tools')\n    \n    try:\n        if args.command == 'list-rules':\n            result = await client.call_tool('list_available_rulesets')\n        elif args.command == 'list-modules':\n            result = await client.call_tool('list_available_modules')\n        elif args.command == 'merge-rules':\n            if not args.name:\n                print('错误: 需要指定规则集名称')\n                return\n            result = await client.call_tool('merge_surge_rules', {'rule_set_name': args.name})\n        elif args.command == 'merge-modules':\n            if not args.name:\n                print('错误: 需要指定模块名称')\n                return\n            result = await client.call_tool('merge_surge_modules', {'module_name': args.name})\n        elif args.command == 'stats':\n            if not args.name:\n                print('错误: 需要指定规则集名称')\n                return\n            result = await client.call_tool('get_rule_stats', {'rule_set_name': args.name})\n        \n        output = json.dumps(result, indent=2, ensure_ascii=False)\n        \n        if args.output:\n            with open(args.output, 'w', encoding='utf-8') as f:\n                f.write(output)\n            print(f'结果已保存到 {args.output}')\n        else:\n            print(output)\n            \n    except Exception as e:\n        print(f'错误: {e}')\n\nif __name__ == '__main__':\n    asyncio.run(main())"}}}, "best_practices": {"error_handling": ["始终使用 try-catch 包装 MCP 工具调用", "检查返回结果中的 'success' 字段", "为网络错误实现重试机制", "记录详细的错误信息用于调试"], "performance": ["对频繁使用的结果进行缓存", "使用异步调用避免阻塞", "批量处理多个请求以提高效率", "监控工具调用的响应时间"], "security": ["验证输入参数以防止注入攻击", "限制对敏感配置文件的访问", "使用安全的网络连接获取规则源", "定期更新依赖包以修复安全漏洞"]}}