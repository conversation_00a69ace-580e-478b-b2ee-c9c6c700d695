{"name": "Augment MCP Configuration for Surge Tools", "description": "Augment 中使用 Surge Tools MCP 服务器的配置示例", "version": "1.0.0", "mcpServers": {"surge-tools": {"name": "Surge Tools MCP Server", "description": "Surge 网络代理配置管理工具", "command": "python", "args": ["surge-tools-mcp-server.py"], "cwd": "/path/to/mcp_export", "env": {"PYTHONPATH": "/path/to/mcp_export", "CONFIG_PATH": "config.yaml", "LOG_LEVEL": "INFO"}, "timeout": 30000, "restart": true, "capabilities": {"tools": true, "resources": false, "prompts": false}}}, "settings": {"autoStart": true, "enableLogging": true, "logLevel": "info"}, "usage_examples": {"basic_queries": ["列出所有可用的 Surge 规则集", "合并 advertising 规则集", "获取 netflix 规则的统计信息", "合并 AppRemoveAds 模块", "显示所有可用的模块"], "advanced_queries": ["比较 advertising 和 apple 规则集的规模差异", "生成包含所有去广告规则的完整配置", "分析 GlobalMedia 和 ChinaMedia 规则集的重叠情况", "创建一个自定义的规则集合并方案", "优化现有的 Surge 配置文件"], "automation_queries": ["创建一个定时更新所有规则集的脚本", "设置规则集变化监控和通知系统", "生成 Surge 配置文件的健康检查报告", "批量处理多个规则集并生成统计报告", "自动化 Surge 模块的部署和更新流程"]}, "recommended_prompts": {"getting_started": ["帮我了解这个 MCP 服务器提供了哪些功能", "展示如何使用这些工具来管理 Surge 配置", "给我一个完整的工作流程示例"], "rule_management": ["合并 {rule_set_name} 规则集并显示统计信息", "比较不同规则集的规模和内容", "帮我选择最适合的规则集组合"], "module_management": ["合并 {module_name} 模块并解释其功能", "分析模块之间的依赖关系", "优化模块配置以提高性能"], "troubleshooting": ["检查规则集配置是否有问题", "诊断模块合并失败的原因", "验证 Surge 配置文件的完整性"]}, "integration_tips": {"best_practices": ["使用自然语言描述您的需求，AI 会自动调用相应的工具", "可以要求 AI 解释工具返回的结果和统计信息", "利用 AI 的分析能力来优化您的 Surge 配置", "结合多个工具调用来完成复杂的配置管理任务"], "workflow_optimization": ["将常用的操作保存为自定义提示词", "使用批量操作来提高效率", "定期检查和更新规则集配置", "建立配置文件的版本控制和备份机制"]}, "troubleshooting": {"common_issues": {"server_not_starting": {"symptoms": ["MCP 服务器无法启动", "连接超时"], "solutions": ["检查 Python 版本是否为 3.8+", "确认所有依赖包已正确安装", "验证配置文件路径是否正确", "检查端口是否被占用"]}, "tool_call_failed": {"symptoms": ["工具调用失败", "返回错误信息"], "solutions": ["检查网络连接是否正常", "验证规则源 URL 是否可访问", "确认配置文件格式正确", "查看详细的错误日志"]}, "config_file_error": {"symptoms": ["配置文件加载失败", "YAML 解析错误"], "solutions": ["验证 YAML 语法是否正确", "检查文件编码是否为 UTF-8", "确认所有必需字段都已填写", "使用 YAML 验证工具检查格式"]}}}}