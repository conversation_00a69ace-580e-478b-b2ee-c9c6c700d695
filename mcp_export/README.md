# Surge Tools MCP Server 使用指南

## 📖 简介

Surge Tools MCP Server 是一个专为 **Augment** 和 **Cursor** 设计的 MCP (Model Context Protocol) 服务器，提供强大的 Surge 网络代理配置管理功能。

### 🚀 主要功能

- **规则合并**: 从多个来源合并 Surge 分流规则
- **模块合并**: 从多个来源合并 Surge 模块配置
- **统计分析**: 提供详细的规则和模块统计信息
- **配置管理**: 支持灵活的 YAML 配置文件
- **AI 集成**: 完美适配 Augment 和 Cursor 开发环境

### 🛠 支持的工具

| 工具名称 | 功能描述 |
|---------|---------|
| `merge_surge_rules` | 合并指定规则集的多个来源规则 |
| `merge_surge_modules` | 合并指定模块的多个来源配置 |
| `list_available_rulesets` | 列出所有可用的规则集 |
| `list_available_modules` | 列出所有可用的模块 |
| `get_rule_stats` | 获取规则集的统计信息 |

## 📦 安装配置

### 1. 环境要求

- Python 3.8+
- pip 包管理器

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置文件

将 `config.yaml` 放置在服务器同级目录下，或通过环境变量指定配置文件路径。

## 🔧 在 Augment 中使用

### 1. 配置 MCP 服务器

在 Augment 的设置中添加 MCP 服务器配置：

```json
{
  "mcpServers": {
    "surge-tools": {
      "command": "python",
      "args": ["/path/to/surge-tools-mcp-server.py"],
      "env": {
        "PYTHONPATH": "/path/to/mcp_export"
      }
    }
  }
}
```

### 2. 使用示例

在 Augment 中，您可以直接使用自然语言与 MCP 服务器交互：

```
"帮我合并 advertising 规则集"
"列出所有可用的模块"
"获取 netflix 规则集的统计信息"
"合并 AppRemoveAds 模块"
```

### 3. 高级用法

```
"合并 youtube 和 netflix 规则，并分析规则数量分布"
"比较 advertising 和 apple 规则集的规模"
"生成一个包含所有去广告模块的配置"
```

## 🎯 在 Cursor 中使用

### 1. 配置 MCP 服务器

在 Cursor 的 `settings.json` 中添加：

```json
{
  "mcp.servers": [
    {
      "name": "surge-tools",
      "command": "python",
      "args": ["/path/to/surge-tools-mcp-server.py"],
      "cwd": "/path/to/mcp_export"
    }
  ]
}
```

### 2. 在代码中使用

Cursor 会自动识别 MCP 工具，您可以在编程时直接调用：

```python
# 示例：在 Python 代码中使用
# Cursor 会提供智能提示和自动补全

# 获取规则集列表
rulesets = await mcp_client.call_tool("list_available_rulesets")

# 合并特定规则集
advertising_rules = await mcp_client.call_tool("merge_surge_rules", {
    "rule_set_name": "advertising"
})
```

### 3. 代码生成辅助

在 Cursor 中，您可以要求 AI 帮助生成使用 MCP 服务的代码：

```
"生成一个脚本来批量处理所有规则集"
"创建一个函数来比较不同模块的配置"
"写一个工具来自动更新 Surge 配置文件"
```

## 📋 可用规则集

| 规则集名称 | 描述 | 用途 |
|-----------|------|------|
| `advertising` | 广告拦截规则 | 拦截广告和隐私追踪 |
| `netflix` | Netflix 分流规则 | Netflix 服务分流 |
| `youtube` | YouTube 分流规则 | YouTube 服务分流 |
| `telegram` | Telegram 分流规则 | Telegram 服务分流 |
| `apple` | Apple 服务规则 | Apple 生态服务分流 |
| `GlobalMedia` | 全球媒体规则 | YouTube, YouTube Music |
| `ChinaMedia` | 中国媒体规则 | 哔哩哔哩, 网易云音乐等 |
| `OpenAI` | AI 服务规则 | OpenAI 等 AI 服务 |

## 🔧 可用模块

| 模块名称 | 描述 | 功能 |
|---------|------|------|
| `black-http-dns` | 拦截 HTTP DNS | 防止 DNS 劫持 |
| `generalSettings` | 通用设置模块 | 基础网络优化 |
| `AppRemoveAds` | 应用去广告模块 | 多应用去广告集合 |

## 💡 使用技巧

### 1. 批量操作

```python
# 获取所有规则集并生成统计报告
async def generate_rules_report():
    rulesets = await mcp_client.call_tool("list_available_rulesets")
    report = {}
    
    for ruleset_name in rulesets['rulesets'].keys():
        stats = await mcp_client.call_tool("get_rule_stats", {
            "rule_set_name": ruleset_name
        })
        report[ruleset_name] = stats
    
    return report
```

### 2. 配置验证

```python
# 验证模块配置的完整性
async def validate_module_config(module_name):
    module_data = await mcp_client.call_tool("merge_surge_modules", {
        "module_name": module_name
    })
    
    if "error" in module_data:
        print(f"模块 {module_name} 配置有误: {module_data['error']}")
        return False
    
    return True
```

### 3. 自动化工作流

在 Augment 或 Cursor 中，您可以创建自动化工作流：

```
"创建一个每日更新 Surge 配置的脚本"
"设置一个监控规则集变化的系统"
"生成一个配置文件健康检查工具"
```

## 🔍 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查 Python 版本 (需要 3.8+)
   - 确认所有依赖已安装
   - 验证配置文件路径

2. **规则合并失败**
   - 检查网络连接
   - 验证规则源 URL 可访问性
   - 查看日志输出

3. **配置文件错误**
   - 验证 YAML 语法
   - 检查必需字段
   - 确认文件编码为 UTF-8

### 调试模式

启用详细日志输出：

```bash
export PYTHONPATH=/path/to/mcp_export
export LOG_LEVEL=DEBUG
python surge-tools-mcp-server.py
```

## 📞 支持与反馈

- **GitHub Issues**: [提交问题](https://github.com/gys619/surge-tools/issues)
- **功能请求**: 通过 GitHub Issues 提交
- **文档改进**: 欢迎提交 Pull Request

## 📄 许可证

本项目采用 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

---

**注意**: 本 MCP 服务器专为 Augment 和 Cursor 优化，提供最佳的 AI 辅助开发体验。
