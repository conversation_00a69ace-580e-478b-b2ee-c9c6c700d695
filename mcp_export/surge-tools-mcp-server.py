#!/usr/bin/env python3
"""
Surge Tools MCP Server - Export Version
适用于 Augment 和 Cursor 的 MCP 服务器

This MCP server provides tools for managing Surge proxy configurations,
including rule merging, module merging, and configuration management.

Features:
- 合并多个 Surge 规则源
- 合并多个 Surge 模块源  
- 列出可用的规则集和模块
- 获取规则统计信息
- 支持自定义配置文件路径
"""

import asyncio
import json
import logging
import os
import sys
import yaml
import requests
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SurgeRuleMerger:
    """Surge 规则合并器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}
    
    def _fetch_rules_from_url(self, url: str) -> List[str]:
        """从URL获取规则"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            rules = []
            for line in response.text.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('//'):
                    rules.append(line)
            
            return rules
        except Exception as e:
            logger.error(f"Failed to fetch rules from {url}: {e}")
            return []
    
    def merge_rules(self, rule_set_name: str) -> Dict[str, Any]:
        """合并指定规则集的规则"""
        if 'rules' not in self.config or 'sources' not in self.config['rules']:
            return {}
        
        rule_config = self.config['rules']['sources'].get(rule_set_name)
        if not rule_config:
            return {}
        
        all_rules = []
        for url in rule_config.get('urls', []):
            rules = self._fetch_rules_from_url(url)
            all_rules.extend(rules)
        
        # 去重并按类型分类
        unique_rules = list(set(all_rules))
        categorized_rules = self._categorize_rules(unique_rules)
        
        # 应用规则优先级
        preference = rule_config.get('rule_preference', [])
        sorted_rules = self._sort_rules_by_preference(categorized_rules, preference)
        
        return {
            'metadata': {
                'name': rule_config.get('name', rule_set_name),
                'author': rule_config.get('author', ''),
                'description': rule_config.get('description', ''),
                'repo': rule_config.get('repo', ''),
                'total_rules': len(unique_rules),
                'sources_count': len(rule_config.get('urls', []))
            },
            'rules': sorted_rules
        }
    
    def _categorize_rules(self, rules: List[str]) -> Dict[str, List[str]]:
        """按规则类型分类"""
        categorized = {}
        
        for rule in rules:
            if not rule.strip():
                continue
                
            rule_type = rule.split(',')[0].strip()
            if rule_type not in categorized:
                categorized[rule_type] = []
            categorized[rule_type].append(rule)
        
        return categorized
    
    def _sort_rules_by_preference(self, categorized_rules: Dict[str, List[str]], 
                                 preference: List[str]) -> Dict[str, List[str]]:
        """按优先级排序规则"""
        sorted_rules = {}
        
        # 按优先级顺序添加
        for rule_type in preference:
            if rule_type in categorized_rules:
                sorted_rules[rule_type] = sorted(categorized_rules[rule_type])
        
        # 添加未在优先级中的规则类型
        for rule_type, rules in categorized_rules.items():
            if rule_type not in sorted_rules:
                sorted_rules[rule_type] = sorted(rules)
        
        return sorted_rules

class SurgeModuleMerger:
    """Surge 模块合并器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}
    
    def _fetch_module_from_url(self, url: str) -> str:
        """从URL获取模块内容"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.error(f"Failed to fetch module from {url}: {e}")
            return ""
    
    def merge_module(self, module_name: str) -> Dict[str, Any]:
        """合并指定模块"""
        if 'modules' not in self.config or 'sources' not in self.config['modules']:
            return {}
        
        module_config = self.config['modules']['sources'].get(module_name)
        if not module_config:
            return {}
        
        all_content = []
        mitm_hostnames = set()
        
        for url in module_config.get('urls', []):
            content = self._fetch_module_from_url(url)
            if content:
                all_content.append(content)
                # 提取MITM hostname
                hostnames = self._extract_mitm_hostnames(content)
                mitm_hostnames.update(hostnames)
        
        merged_content = self._merge_module_content(all_content, module_config)
        
        return {
            'metadata': {
                'name': module_config.get('name', module_name),
                'desc': module_config.get('desc', ''),
                'author': module_config.get('author', ''),
                'repo': module_config.get('repo', ''),
                'sources_count': len(module_config.get('urls', []))
            },
            'content': merged_content,
            'mitm_hostnames': list(mitm_hostnames)
        }
    
    def _extract_mitm_hostnames(self, content: str) -> List[str]:
        """提取MITM hostname"""
        hostnames = []
        in_mitm_section = False
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('[MITM]'):
                in_mitm_section = True
                continue
            elif line.startswith('[') and in_mitm_section:
                in_mitm_section = False
                continue
            
            if in_mitm_section and line.startswith('hostname'):
                # 解析hostname行
                hostname_part = line.split('=', 1)[1].strip() if '=' in line else ''
                if hostname_part:
                    hosts = [h.strip() for h in hostname_part.split(',') if h.strip()]
                    hostnames.extend(hosts)
        
        return hostnames
    
    def _merge_module_content(self, contents: List[str], config: Dict[str, Any]) -> str:
        """合并模块内容"""
        if not contents:
            return ""
        
        # 简单合并，实际实现可以更复杂
        merged = f"#!name={config.get('name', 'Merged Module')}\n"
        merged += f"#!desc={config.get('desc', 'Merged from multiple sources')}\n"
        merged += f"#!author={config.get('author', 'surge-tools')}\n\n"
        
        for i, content in enumerate(contents):
            if i > 0:
                merged += "\n# ========== Source {} ==========\n".format(i + 1)
            merged += content
        
        return merged
    
    def format_module_content(self, merged_data: Dict[str, Any]) -> str:
        """格式化模块内容"""
        return merged_data.get('content', '')

class SurgeToolsMCPServer:
    """Surge Tools MCP 服务器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self.rule_merger = None
        self.module_merger = None
        self._initialize_mergers()
    
    def _initialize_mergers(self):
        """初始化合并器"""
        try:
            if os.path.exists(self.config_path):
                self.rule_merger = SurgeRuleMerger(self.config_path)
                self.module_merger = SurgeModuleMerger(self.config_path)
                logger.info(f"Initialized mergers with config: {self.config_path}")
            else:
                logger.warning(f"Config file not found: {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to initialize mergers: {e}")
    
    async def merge_surge_rules(self, rule_set_name: str) -> Dict[str, Any]:
        """合并 Surge 规则"""
        if not self.rule_merger:
            raise ValueError("Rule merger not initialized. Check config file.")
        
        try:
            merged_data = self.rule_merger.merge_rules(rule_set_name)
            if not merged_data:
                return {"error": f"Failed to merge rules for {rule_set_name}"}
            
            # 计算统计信息
            rules = merged_data['rules']
            stats = {}
            total = 0
            for rule_type, rule_list in rules.items():
                count = len(rule_list)
                stats[rule_type] = count
                total += count
            stats['TOTAL'] = total
            
            return {
                "success": True,
                "rule_set_name": rule_set_name,
                "metadata": merged_data['metadata'],
                "statistics": stats,
                "rules": merged_data['rules']
            }
        except Exception as e:
            logger.error(f"Error merging rules for {rule_set_name}: {e}")
            return {"error": str(e)}
    
    async def merge_surge_modules(self, module_name: str) -> Dict[str, Any]:
        """合并 Surge 模块"""
        if not self.module_merger:
            raise ValueError("Module merger not initialized. Check config file.")
        
        try:
            merged_data = self.module_merger.merge_module(module_name)
            if not merged_data:
                return {"error": f"Failed to merge module {module_name}"}
            
            return {
                "success": True,
                "module_name": module_name,
                "metadata": merged_data['metadata'],
                "content": self.module_merger.format_module_content(merged_data),
                "mitm_hostnames": merged_data.get('mitm_hostnames', [])
            }
        except Exception as e:
            logger.error(f"Error merging module {module_name}: {e}")
            return {"error": str(e)}
    
    async def list_available_rulesets(self) -> Dict[str, Any]:
        """列出所有可用的规则集"""
        if not self.rule_merger:
            return {"error": "Rule merger not initialized"}
        
        try:
            rulesets = {}
            if 'rules' in self.rule_merger.config and 'sources' in self.rule_merger.config['rules']:
                for name, config in self.rule_merger.config['rules']['sources'].items():
                    rulesets[name] = {
                        "name": config.get('name', name),
                        "description": config.get('description', ''),
                        "author": config.get('author', ''),
                        "urls_count": len(config.get('urls', []))
                    }
            
            return {
                "success": True,
                "rulesets": rulesets,
                "total_count": len(rulesets)
            }
        except Exception as e:
            return {"error": str(e)}
    
    async def list_available_modules(self) -> Dict[str, Any]:
        """列出所有可用的模块"""
        if not self.module_merger:
            return {"error": "Module merger not initialized"}
        
        try:
            modules = {}
            if 'modules' in self.module_merger.config and 'sources' in self.module_merger.config['modules']:
                for name, config in self.module_merger.config['modules']['sources'].items():
                    modules[name] = {
                        "name": config.get('name', name),
                        "description": config.get('desc', ''),
                        "author": config.get('author', ''),
                        "urls_count": len(config.get('urls', []))
                    }
            
            return {
                "success": True,
                "modules": modules,
                "total_count": len(modules)
            }
        except Exception as e:
            return {"error": str(e)}
    
    async def get_rule_stats(self, rule_set_name: str) -> Dict[str, Any]:
        """获取规则集统计信息"""
        try:
            merged_data = await self.merge_surge_rules(rule_set_name)
            if "error" in merged_data:
                return merged_data
            
            return {
                "success": True,
                "rule_set_name": rule_set_name,
                "statistics": merged_data["statistics"],
                "metadata": merged_data["metadata"]
            }
        except Exception as e:
            return {"error": str(e)}

# 创建 MCP 服务器
server = Server("surge-tools")
surge_tools = SurgeToolsMCPServer()

@server.list_tools()
async def handle_list_tools() -> ListToolsResult:
    """列出可用工具"""
    return ListToolsResult(
        tools=[
            Tool(
                name="merge_surge_rules",
                description="合并多个来源的 Surge 规则到指定规则集",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "rule_set_name": {
                            "type": "string",
                            "description": "要合并的规则集名称 (例如: 'advertising', 'apple', 'telegram', 'netflix', 'youtube')"
                        }
                    },
                    "required": ["rule_set_name"]
                }
            ),
            Tool(
                name="merge_surge_modules",
                description="合并多个来源的 Surge 模块到指定模块",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "module_name": {
                            "type": "string",
                            "description": "要合并的模块名称 (例如: 'black-http-dns', 'generalSettings', 'AppRemoveAds')"
                        }
                    },
                    "required": ["module_name"]
                }
            ),
            Tool(
                name="list_available_rulesets",
                description="列出配置文件中所有可用的规则集",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            Tool(
                name="list_available_modules",
                description="列出配置文件中所有可用的模块",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            Tool(
                name="get_rule_stats",
                description="获取指定规则集的统计信息",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "rule_set_name": {
                            "type": "string",
                            "description": "要获取统计信息的规则集名称"
                        }
                    },
                    "required": ["rule_set_name"]
                }
            )
        ]
    )

@server.call_tool()
async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
    """处理工具调用"""
    try:
        if request.name == "merge_surge_rules":
            rule_set_name = request.arguments.get("rule_set_name")
            if not rule_set_name:
                raise ValueError("rule_set_name is required")
            
            result = await surge_tools.merge_surge_rules(rule_set_name)
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2, ensure_ascii=False))])
        
        elif request.name == "merge_surge_modules":
            module_name = request.arguments.get("module_name")
            if not module_name:
                raise ValueError("module_name is required")
            
            result = await surge_tools.merge_surge_modules(module_name)
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2, ensure_ascii=False))])
        
        elif request.name == "list_available_rulesets":
            result = await surge_tools.list_available_rulesets()
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2, ensure_ascii=False))])
        
        elif request.name == "list_available_modules":
            result = await surge_tools.list_available_modules()
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2, ensure_ascii=False))])
        
        elif request.name == "get_rule_stats":
            rule_set_name = request.arguments.get("rule_set_name")
            if not rule_set_name:
                raise ValueError("rule_set_name is required")
            
            result = await surge_tools.get_rule_stats(rule_set_name)
            return CallToolResult(content=[TextContent(type="text", text=json.dumps(result, indent=2, ensure_ascii=False))])
        
        else:
            raise ValueError(f"Unknown tool: {request.name}")
    
    except Exception as e:
        logger.error(f"Error handling tool call {request.name}: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=json.dumps({"error": str(e)}, indent=2, ensure_ascii=False))],
            isError=True
        )

async def main():
    """主入口点"""
    # 切换到项目目录
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="surge-tools",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
