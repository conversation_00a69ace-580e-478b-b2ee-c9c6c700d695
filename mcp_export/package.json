{"name": "surge-tools-mcp-server", "version": "1.0.0", "description": "MCP Server for Surge Tools - Network proxy configuration management (适用于 Augment 和 Cursor)", "main": "surge-tools-mcp-server.py", "scripts": {"start": "python surge-tools-mcp-server.py", "install-deps": "pip install -r requirements.txt", "test": "python -m pytest tests/ -v", "lint": "python -m flake8 surge-tools-mcp-server.py"}, "keywords": ["mcp", "surge", "proxy", "network", "configuration", "rules", "modules", "augment", "cursor", "ai-tools"], "author": "gys619", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/gys619/surge-tools"}, "bugs": {"url": "https://github.com/gys619/surge-tools/issues"}, "homepage": "https://github.com/gys619/surge-tools#readme", "mcp": {"server": {"command": "python", "args": ["surge-tools-mcp-server.py"], "env": {"PYTHONPATH": "."}}}, "engines": {"python": ">=3.8"}, "devDependencies": {"pytest": ">=7.0.0", "flake8": ">=5.0.0"}}