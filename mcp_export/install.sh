#!/bin/bash

# Surge Tools MCP Server 安装脚本
# 适用于 Augment 和 Cursor

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_warning() {
    print_message $YELLOW "⚠️  $1"
}

print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 检查 Python 版本
check_python() {
    print_info "检查 Python 版本..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            print_success "Python $PYTHON_VERSION 版本符合要求 (需要 3.8+)"
            PYTHON_CMD="python3"
        else
            print_error "Python 版本 $PYTHON_VERSION 不符合要求，需要 Python 3.8+"
            exit 1
        fi
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            print_success "Python $PYTHON_VERSION 版本符合要求 (需要 3.8+)"
            PYTHON_CMD="python"
        else
            print_error "Python 版本 $PYTHON_VERSION 不符合要求，需要 Python 3.8+"
            exit 1
        fi
    else
        print_error "未找到 Python，请先安装 Python 3.8+"
        exit 1
    fi
}

# 检查 pip
check_pip() {
    print_info "检查 pip..."
    
    if command -v pip3 &> /dev/null; then
        print_success "找到 pip3"
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        print_success "找到 pip"
        PIP_CMD="pip"
    else
        print_error "未找到 pip，请先安装 pip"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    print_info "安装 Python 依赖包..."
    
    if [ -f "requirements.txt" ]; then
        $PIP_CMD install -r requirements.txt
        print_success "依赖包安装完成"
    else
        print_error "未找到 requirements.txt 文件"
        exit 1
    fi
}

# 验证安装
verify_installation() {
    print_info "验证安装..."
    
    # 检查主要依赖包
    local packages=("mcp" "pydantic" "requests" "PyYAML")
    
    for package in "${packages[@]}"; do
        if $PYTHON_CMD -c "import $package" 2>/dev/null; then
            print_success "$package 已正确安装"
        else
            print_error "$package 安装失败"
            exit 1
        fi
    done
}

# 创建启动脚本
create_launcher() {
    print_info "创建启动脚本..."
    
    local script_dir=$(pwd)
    local launcher_script="start_surge_mcp.sh"
    
    cat > $launcher_script << EOF
#!/bin/bash
# Surge Tools MCP Server 启动脚本

cd "$script_dir"
export PYTHONPATH="$script_dir"
$PYTHON_CMD surge-tools-mcp-server.py
EOF
    
    chmod +x $launcher_script
    print_success "启动脚本已创建: $launcher_script"
}

# 生成配置示例
generate_config_examples() {
    print_info "生成配置示例..."
    
    # Augment 配置示例
    local augment_config="augment_mcp_config.json"
    local script_dir=$(pwd)
    
    cat > $augment_config << EOF
{
  "mcpServers": {
    "surge-tools": {
      "command": "$PYTHON_CMD",
      "args": ["$script_dir/surge-tools-mcp-server.py"],
      "cwd": "$script_dir",
      "env": {
        "PYTHONPATH": "$script_dir"
      }
    }
  }
}
EOF
    
    print_success "Augment 配置示例已生成: $augment_config"
    
    # Cursor 配置示例
    local cursor_config="cursor_mcp_config.json"
    
    cat > $cursor_config << EOF
{
  "mcp": {
    "servers": [
      {
        "name": "surge-tools",
        "command": "$PYTHON_CMD",
        "args": ["$script_dir/surge-tools-mcp-server.py"],
        "cwd": "$script_dir",
        "env": {
          "PYTHONPATH": "$script_dir"
        }
      }
    ]
  }
}
EOF
    
    print_success "Cursor 配置示例已生成: $cursor_config"
}

# 测试服务器
test_server() {
    print_info "测试 MCP 服务器..."
    
    # 创建测试脚本
    cat > test_mcp.py << 'EOF'
#!/usr/bin/env python3
import asyncio
import json
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_server():
    try:
        # 导入服务器模块
        from surge_tools_mcp_server import SurgeToolsMCPServer
        
        # 创建服务器实例
        server = SurgeToolsMCPServer()
        
        # 测试基本功能
        print("测试列出规则集...")
        result = await server.list_available_rulesets()
        if result.get('success'):
            print(f"✅ 找到 {result.get('total_count', 0)} 个规则集")
        else:
            print(f"❌ 列出规则集失败: {result.get('error', '未知错误')}")
            return False
        
        print("测试列出模块...")
        result = await server.list_available_modules()
        if result.get('success'):
            print(f"✅ 找到 {result.get('total_count', 0)} 个模块")
        else:
            print(f"❌ 列出模块失败: {result.get('error', '未知错误')}")
            return False
        
        print("✅ MCP 服务器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_server())
    sys.exit(0 if success else 1)
EOF
    
    if $PYTHON_CMD test_mcp.py; then
        print_success "MCP 服务器测试通过"
    else
        print_error "MCP 服务器测试失败"
        exit 1
    fi
    
    # 清理测试文件
    rm -f test_mcp.py
}

# 显示使用说明
show_usage() {
    print_info "安装完成！使用说明："
    echo
    echo "1. 在 Augment 中使用："
    echo "   - 将 augment_mcp_config.json 中的配置添加到 Augment 设置中"
    echo "   - 重启 Augment"
    echo
    echo "2. 在 Cursor 中使用："
    echo "   - 将 cursor_mcp_config.json 中的配置添加到 Cursor 设置中"
    echo "   - 重启 Cursor"
    echo
    echo "3. 手动启动服务器："
    echo "   ./start_surge_mcp.sh"
    echo
    echo "4. 可用的工具："
    echo "   - merge_surge_rules: 合并规则集"
    echo "   - merge_surge_modules: 合并模块"
    echo "   - list_available_rulesets: 列出规则集"
    echo "   - list_available_modules: 列出模块"
    echo "   - get_rule_stats: 获取规则统计"
    echo
    print_success "享受使用 Surge Tools MCP Server！"
}

# 主函数
main() {
    print_info "开始安装 Surge Tools MCP Server..."
    echo
    
    check_python
    check_pip
    install_dependencies
    verify_installation
    create_launcher
    generate_config_examples
    test_server
    
    echo
    show_usage
}

# 运行主函数
main "$@"
