{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@context7/mcp-server"], "description": "Context7 library documentation server for accessing up-to-date documentation"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Sequential thinking server for complex problem-solving and analysis"}, "mcp-feedback-enhanced": {"command": "python", "args": ["-m", "mcp_feedback_enhanced"], "description": "Enhanced feedback collection server with Web UI interface and system info"}, "playwright": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-playwright"], "description": "Playwright automation server for browser interactions and testing"}, "mcp-server-time": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "description": "Time server for timezone conversions and current time queries"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>/vscodeProjects/surge-tools"}, "description": "Filesystem server for file operations within allowed directories"}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}, "description": "Brave search server for web search capabilities"}, "web-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-web-search"], "env": {"GOOGLE_API_KEY": "your-google-api-key-here", "GOOGLE_CSE_ID": "your-google-cse-id-here"}, "description": "Google Custom Search server for web search functionality"}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "Fetch server for retrieving web content and converting to markdown"}}, "tools": {"context7": ["resolve-library-id_context7", "get-library-docs_context7"], "sequential-thinking": ["sequentialthinking_sequential-thinking"], "mcp-feedback-enhanced": ["interactive_feedback_mcp-feedback-enhanced", "get_system_info_mcp-feedback-enhanced"], "playwright": ["browser_close_playwright", "browser_resize_playwright", "browser_console_messages_playwright", "browser_handle_dialog_playwright", "browser_file_upload_playwright", "browser_install_playwright", "browser_press_key_playwright", "browser_navigate_playwright", "browser_navigate_back_playwright", "browser_navigate_forward_playwright", "browser_network_requests_playwright", "browser_pdf_save_playwright", "browser_take_screenshot_playwright", "browser_snapshot_playwright", "browser_click_playwright", "browser_drag_playwright", "browser_hover_playwright", "browser_type_playwright", "browser_select_option_playwright", "browser_tab_list_playwright", "browser_tab_new_playwright", "browser_tab_select_playwright", "browser_tab_close_playwright", "browser_generate_playwright_test_playwright", "browser_wait_for_playwright"], "mcp-server-time": ["get_current_time_mcp-server-time", "convert_time_mcp-server-time"], "filesystem": ["str-replace-editor", "open-browser", "diagnostics", "read-terminal", "launch-process", "kill-process", "read-process", "write-process", "list-processes", "remove-files", "save-file"], "web-search": ["web-search"], "fetch": ["web-fetch"], "augment-built-in": ["codebase-retrieval", "view_tasklist", "reorganize_tasklist", "update_tasks", "add_tasks", "remember", "render-mermaid", "view-range-untruncated", "search-untruncated", "view"]}, "configuration_notes": {"environment_setup": {"note": "Some servers require API keys to be set as environment variables", "required_env_vars": {"BRAVE_API_KEY": "Required for Brave search functionality", "GOOGLE_API_KEY": "Required for Google Custom Search", "GOOGLE_CSE_ID": "Required for Google Custom Search Engine ID"}}, "installation": {"note": "Most servers are installed via npx for easy setup", "python_servers": ["mcp_feedback_enhanced"], "npm_servers": ["@context7/mcp-server", "@modelcontextprotocol/server-sequential-thinking", "@modelcontextprotocol/server-playwright", "@modelcontextprotocol/server-time", "@modelcontextprotocol/server-filesystem", "@modelcontextprotocol/server-brave-search", "@modelcontextprotocol/server-web-search", "@modelcontextprotocol/server-fetch"]}, "security": {"filesystem_access": "Limited to specified directories via ALLOWED_DIRECTORIES", "api_keys": "Store securely and never commit to version control"}}}