{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Sequential thinking server for complex problem-solving"}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}, "description": "Brave search server (requires API key)"}, "context7": {"command": "npx", "args": ["-y", "@context7/mcp-server"], "description": "Library documentation server"}, "playwright": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-playwright"], "description": "Browser automation server"}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "Web content fetching server"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>/vscodeProjects/surge-tools"}, "description": "File operations (adjust ALLOWED_DIRECTORIES path)"}}, "troubleshooting": {"if_only_some_services_work": {"check_environment_variables": ["echo $BRAVE_API_KEY", "echo $GOOGLE_API_KEY"], "test_individual_services": ["npx -y @context7/mcp-server --version", "npx -y @modelcontextprotocol/server-playwright --version"], "common_issues": {"network_problems": "Check internet connection and proxy settings", "permission_issues": "Ensure npx has proper permissions", "missing_dependencies": "Some services may require additional system dependencies"}}, "step_by_step_testing": {"1": "Start with sequential-thinking (should work without API keys)", "2": "Add brave-search (requires BRAVE_API_KEY)", "3": "Add context7 (should work without API keys)", "4": "Add playwright (may require browser installation)", "5": "Add fetch (should work without API keys)", "6": "Add filesystem (adjust path as needed)"}}, "setup_instructions": {"1_set_environment_variables": {"bash": "export BRAVE_API_KEY='your-actual-api-key'", "windows": "set BRAVE_API_KEY=your-actual-api-key", "permanent": "Add to ~/.bashrc or ~/.zshrc"}, "2_test_services": {"command": "Test each service individually before adding to config"}, "3_adjust_paths": {"filesystem": "Change ALLOWED_DIRECTORIES to your actual project path"}}}