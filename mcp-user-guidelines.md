# MCP (Model Context Protocol) User Guidelines

## Overview
This document provides comprehensive guidelines for using MCP services with Augment Agent and development tools like Cursor.

## Available MCP Services

### 1. Context7 Library Documentation
**Purpose**: Access up-to-date documentation for popular libraries and frameworks
**Usage**:
- Resolve library names to get documentation
- Fetch specific documentation topics
- Get code examples and API references

**Example Commands**:
```
"Show me React hooks documentation"
"Get Next.js routing examples"
"Find MongoDB connection patterns"
```

### 2. Sequential Thinking
**Purpose**: Complex problem-solving through structured thinking processes
**Usage**:
- Break down complex problems into steps
- Plan multi-step solutions
- Analyze problems with course correction
- Handle uncertain or evolving requirements

**Best For**:
- Architecture decisions
- Debugging complex issues
- Planning large features
- Code refactoring strategies

### 3. Enhanced Feedback Collection
**Purpose**: Interactive feedback collection with Web UI support
**Features**:
- Text and image feedback collection
- Command execution verification
- System environment detection
- Progress tracking

**Usage**:
- Verify implementation results
- Collect user feedback on changes
- Test functionality interactively

### 4. Playwright Browser Automation
**Purpose**: Browser testing and automation
**Capabilities**:
- Navigate web pages
- Take screenshots and snapshots
- Interact with web elements
- Generate automated tests
- Handle file uploads and downloads

**Common Use Cases**:
- E2E testing
- Web scraping
- UI testing
- Form automation

### 5. Time Services
**Purpose**: Time zone conversions and current time queries
**Features**:
- Get current time in any timezone
- Convert between timezones
- Handle scheduling across regions

### 6. Filesystem Operations
**Purpose**: Safe file and directory operations
**Security**: Limited to allowed directories only
**Capabilities**:
- Read/write files
- Directory navigation
- File search and manipulation
- Code editing with syntax awareness

### 7. Web Search & Fetch
**Purpose**: Web content retrieval and search
**Services**:
- Google Custom Search
- Brave Search
- Web content fetching and markdown conversion

## Setup Instructions

### 1. Basic Installation
Most MCP servers install automatically via npx:
```bash
# No manual installation needed for most services
# They install on-demand via npx
```

### 2. Python-based Services
For Python MCP servers:
```bash
pip install mcp_feedback_enhanced
```

### 3. Environment Variables
Set required API keys:
```bash
export BRAVE_API_KEY="your-brave-api-key"
export GOOGLE_API_KEY="your-google-api-key"
export GOOGLE_CSE_ID="your-google-cse-id"
```

### 4. Cursor Integration
Add to your Cursor settings:
```json
{
  "mcp": {
    "servers": {
      // Copy configuration from mcp-config-export.json
    }
  }
}
```

## Best Practices

### 1. Security
- Never commit API keys to version control
- Use environment variables for sensitive data
- Limit filesystem access to necessary directories
- Review permissions before granting access

### 2. Performance
- Use specific queries for better results
- Cache frequently accessed documentation
- Batch operations when possible
- Monitor API usage limits

### 3. Development Workflow
- Start with documentation lookup for unfamiliar APIs
- Use sequential thinking for complex problems
- Test changes with interactive feedback
- Automate repetitive tasks with Playwright

### 4. Troubleshooting
- Check environment variables if services fail
- Verify network connectivity for web services
- Review error messages for specific issues
- Use system info tool for environment debugging

## Common Workflows

### 1. Learning New Technology
1. Use Context7 to get documentation
2. Ask for specific examples and patterns
3. Implement with guidance
4. Test with interactive feedback

### 2. Debugging Issues
1. Use sequential thinking to analyze the problem
2. Search web for similar issues
3. Test solutions interactively
4. Document the solution

### 3. Building Features
1. Plan with task management tools
2. Research APIs and patterns
3. Implement incrementally
4. Test with automation tools

### 4. Code Review & Refactoring
1. Analyze code structure
2. Plan improvements systematically
3. Make changes incrementally
4. Verify with tests and feedback

## Integration Tips

### With Cursor
- Configure MCP servers in settings
- Use context-aware suggestions
- Leverage real-time documentation
- Automate testing workflows

### With Augment
- Combine with codebase retrieval
- Use task management for complex work
- Leverage memory for long-term context
- Integrate with development tools

## Limitations & Considerations

### Rate Limits
- Web search APIs have daily limits
- Documentation services may throttle requests
- Plan usage accordingly

### Network Dependencies
- Most services require internet connectivity
- Some features work offline (filesystem, thinking)
- Consider fallback strategies

### Privacy
- Web searches are logged by providers
- Local operations remain private
- Review data handling policies

## Support & Resources

### Documentation
- Each MCP server has its own documentation
- Check npm packages for detailed guides
- Review source code for advanced usage

### Community
- MCP protocol specification
- Model Context Protocol GitHub
- Augment Code documentation
- Cursor community forums

## Version Compatibility
- MCP servers update independently
- Use npx for latest versions
- Pin versions for production use
- Test updates in development first
