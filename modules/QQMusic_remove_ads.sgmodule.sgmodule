#!name=QQ音乐去广告
#!desc=🔗 [2025/6/12 14:42:16] 过滤QQ音乐广告
#!HASH=d0c8bfcb9f55dc9bcfdd6311bb40f32d
#!author=可莉🅥[https://github.com/luestr/ProxyResource/blob/main/README.md]
#!icon=https://raw.githubusercontent.com/luestr/IconResource/main/App_icon/120px/QQMusic.png
#!openUrl=https://apps.apple.com/app/id414603431
#!tag=去广告
#!loon_version=3.2.4(787)
#!homepage=https://github.com/luestr/ProxyResource/blob/main/README.md
#!date=2025-05-13 15:17:08

[Rule]
DOMAIN,adstats.tencentmusic.com,REJECT
DOMAIN,ad.tencentmusic.com,REJECT
DOMAIN,adcdn.tencentmusic.com,REJECT
DOMAIN,adcdn6.tencentmusic.com,REJECT
DOMAIN,adexpo.tencentmusic.com,REJECT
DOMAIN,adclick.tencentmusic.com,REJECT
DOMAIN,otheve.beacon.qq.com,REJECT
DOMAIN,monitor.music.qq.com,REJECT
DOMAIN,stat.y.qq.com,REJECT
DOMAIN,tmead.y.qq.com,REJECT
DOMAIN,tmeadquic.y.qq.com,REJECT
DOMAIN,oth.str.mdt.qq.com,REJECT
DOMAIN,h.trace.qq.com,REJECT
DOMAIN,sdk.e.qq.com,REJECT
DOMAIN,p.l.qq.com,REJECT
DOMAIN,us.l.qq.com,REJECT
DOMAIN,tangram.e.qq.com,REJECT
DOMAIN,tmeadbak.y.qq.com,REJECT
DOMAIN-SUFFIX,imtmp.net,REJECT

# 🔗 模块链接
#SUBSCRIBED http://script.hub/file/_start_/https://kelee.one/Tool/Loon/Plugin/QQMusic_remove_ads.plugin/_end_/QQMusic_remove_ads.sgmodule.txt?type=loon-plugin&target=surge-module&del=true&jqEnabled=true
